"""
Simple Autoencoder implementation for well log imputation.
This is a simplified version that doesn't depend on PyPOTS.
"""

import torch
import torch.nn as nn
import numpy as np
from .neuralnet import AENN, _reconstruction_loss

class SimpleAutoencoder:
    """
    A simplified autoencoder for well log imputation.
    """

    def __init__(self, n_features=4, sequence_len=64, encoding_dim=32,
                 epochs=50, batch_size=32, learning_rate=0.0001):
        """
        Initialize the autoencoder.

        Args:
            n_features: Number of features in the input
            sequence_len: Length of input sequences
            encoding_dim: Dimension of the encoded representation
            epochs: Number of training epochs
            batch_size: Batch size for training
            learning_rate: Learning rate for optimization
        """
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.encoding_dim = encoding_dim
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate

        # Create the autoencoder model
        input_size = sequence_len * n_features
        self.model = AENN(
            enc_layers=[128, 64],
            enc_activation='relu',
            input_size=input_size,
            latent_dim=encoding_dim
        )

        # Initialize weights properly
        self._initialize_weights()

        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss(reduction='none')  # Use reduction='none' for masking
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10
        )

    def _initialize_weights(self):
        """Initialize model weights to prevent gradient explosion."""
        for module in self.model.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
    def fit(self, train_data, truth_data, epochs=None, batch_size=None):
        """
        Train the autoencoder.

        Args:
            train_data: Training data with missing values (NaN values)
            truth_data: Complete data for training
            epochs: Number of epochs (optional)
            batch_size: Batch size (optional)
        """
        # Input validation
        if not isinstance(train_data, torch.Tensor) or not isinstance(truth_data, torch.Tensor):
            raise TypeError("train_data and truth_data must be torch.Tensor")

        if train_data.shape != truth_data.shape:
            raise ValueError(f"Shape mismatch: train_data {train_data.shape} vs truth_data {truth_data.shape}")

        if len(train_data.shape) != 3:
            raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {train_data.shape}")

        if train_data.shape[1] != self.sequence_len or train_data.shape[2] != self.n_features:
            raise ValueError(f"Data shape {train_data.shape} doesn't match model config (seq_len={self.sequence_len}, n_features={self.n_features})")

        if epochs is None:
            epochs = self.epochs
        if batch_size is None:
            batch_size = self.batch_size

        # Validate epochs and batch_size
        if epochs <= 0:
            raise ValueError("epochs must be positive")
        if batch_size <= 0:
            raise ValueError("batch_size must be positive")

        self.model.train()

        # Prepare data: replace NaN with zeros in training data, create masks
        train_clean = torch.nan_to_num(train_data, nan=0.0)  # Replace NaN with 0
        missing_mask = torch.isnan(train_data).float()  # 1 where data is missing

        # Flatten the input data
        train_flat = train_clean.view(train_clean.size(0), -1)
        truth_flat = truth_data.view(truth_data.size(0), -1)
        mask_flat = missing_mask.view(missing_mask.size(0), -1)

        print(f"Training autoencoder for {epochs} epochs...")
        print(f"Data shape: {train_flat.shape}")
        print(f"Missing data percentage: {missing_mask.mean().item():.2%}")

        best_loss = float('inf')
        patience_counter = 0
        patience_limit = 15

        for epoch in range(epochs):
            total_loss = 0
            num_batches = (len(train_data) + batch_size - 1) // batch_size

            for i in range(num_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(train_data))

                batch_train = train_flat[start_idx:end_idx]
                batch_truth = truth_flat[start_idx:end_idx]
                batch_mask = mask_flat[start_idx:end_idx]

                # Forward pass
                self.optimizer.zero_grad()
                reconstructed = self.model(batch_train)

                # Compute masked loss (only on non-missing values)
                loss_per_element = self.criterion(reconstructed, batch_truth)
                # Create inverse mask (1 where data is present, 0 where missing)
                valid_mask = 1.0 - batch_mask
                masked_loss = loss_per_element * valid_mask

                # Average loss only over valid (non-missing) elements
                valid_count = valid_mask.sum()
                if valid_count > 0:
                    loss = masked_loss.sum() / valid_count
                else:
                    loss = torch.tensor(0.0, requires_grad=True)

                # Check for NaN loss and gradients
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"Warning: Invalid loss detected at epoch {epoch}, batch {i}: {loss.item()}")
                    continue

                # Backward pass with gradient clipping
                loss.backward()

                # Check for NaN gradients
                has_nan_grad = False
                for param in self.model.parameters():
                    if param.grad is not None and torch.isnan(param.grad).any():
                        has_nan_grad = True
                        break

                if has_nan_grad:
                    print(f"Warning: NaN gradients detected at epoch {epoch}, batch {i}")
                    self.optimizer.zero_grad()
                    continue

                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()

            avg_loss = total_loss / num_batches if num_batches > 0 else float('inf')

            # Learning rate scheduling
            self.scheduler.step(avg_loss)

            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= patience_limit:
                print(f"Early stopping at epoch {epoch} (patience limit reached)")
                break

            if epoch % 10 == 0:
                current_lr = self.optimizer.param_groups[0]['lr']
                print(f"Epoch {epoch}/{epochs}, Loss: {avg_loss:.6f}, LR: {current_lr:.2e}")

        print(f"Training completed! Best loss: {best_loss:.6f}")
    
    def predict(self, data):
        """
        Predict/impute missing values.

        Args:
            data: Input data with missing values (NaN values)

        Returns:
            Imputed data where missing values are filled with predictions
        """
        # Input validation
        if not isinstance(data, torch.Tensor):
            raise TypeError("data must be torch.Tensor")

        if len(data.shape) != 3:
            raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {data.shape}")

        if data.shape[1] != self.sequence_len or data.shape[2] != self.n_features:
            raise ValueError(f"Data shape {data.shape} doesn't match model config (seq_len={self.sequence_len}, n_features={self.n_features})")

        self.model.eval()

        with torch.no_grad():
            # Create mask for missing values
            missing_mask = torch.isnan(data)

            # Replace NaN with zeros for model input
            data_clean = torch.nan_to_num(data, nan=0.0)

            # Flatten the input data
            data_flat = data_clean.view(data_clean.size(0), -1)

            # Get reconstructed data
            reconstructed = self.model(data_flat)

            # Reshape back to original shape
            reconstructed = reconstructed.view(data.shape)

            # Create imputed data: use original values where available, predictions where missing
            imputed_data = data.clone()
            imputed_data[missing_mask] = reconstructed[missing_mask]

        return imputed_data

class SimpleUNet:
    """
    A simplified U-Net placeholder for well log imputation.
    """

    def __init__(self, n_features=4, sequence_len=64, epochs=50, batch_size=32, learning_rate=0.0001):
        """
        Initialize the U-Net.
        """
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate

        # For now, use a simple linear model as placeholder
        self.model = nn.Sequential(
            nn.Linear(sequence_len * n_features, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, sequence_len * n_features)
        )

        # Initialize weights properly
        self._initialize_weights()

        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss(reduction='none')  # Use reduction='none' for masking
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10
        )

    def _initialize_weights(self):
        """Initialize model weights to prevent gradient explosion."""
        for module in self.model.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
    def fit(self, train_data, truth_data, epochs=None, batch_size=None):
        """
        Train the U-Net.
        """
        if epochs is None:
            epochs = self.epochs
        if batch_size is None:
            batch_size = self.batch_size

        self.model.train()

        # Prepare data: replace NaN with zeros in training data, create masks
        train_clean = torch.nan_to_num(train_data, nan=0.0)  # Replace NaN with 0
        missing_mask = torch.isnan(train_data).float()  # 1 where data is missing

        # Flatten the input data
        train_flat = train_clean.view(train_clean.size(0), -1)
        truth_flat = truth_data.view(truth_data.size(0), -1)
        mask_flat = missing_mask.view(missing_mask.size(0), -1)

        print(f"Training U-Net for {epochs} epochs...")
        print(f"Data shape: {train_flat.shape}")
        print(f"Missing data percentage: {missing_mask.mean().item():.2%}")

        for epoch in range(epochs):
            total_loss = 0
            num_batches = (len(train_data) + batch_size - 1) // batch_size

            for i in range(num_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(train_data))

                batch_train = train_flat[start_idx:end_idx]
                batch_truth = truth_flat[start_idx:end_idx]
                batch_mask = mask_flat[start_idx:end_idx]

                # Forward pass
                self.optimizer.zero_grad()
                reconstructed = self.model(batch_train)

                # Compute masked loss (only on non-missing values)
                loss_per_element = self.criterion(reconstructed, batch_truth)
                # Create inverse mask (1 where data is present, 0 where missing)
                valid_mask = 1.0 - batch_mask
                masked_loss = loss_per_element * valid_mask

                # Average loss only over valid (non-missing) elements
                valid_count = valid_mask.sum()
                if valid_count > 0:
                    loss = masked_loss.sum() / valid_count
                else:
                    loss = torch.tensor(0.0, requires_grad=True)

                # Check for NaN loss
                if torch.isnan(loss):
                    print(f"Warning: NaN loss detected at epoch {epoch}, batch {i}")
                    continue

                # Backward pass with gradient clipping
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()

            if epoch % 10 == 0:
                avg_loss = total_loss / num_batches if num_batches > 0 else 0
                print(f"Epoch {epoch}/{epochs}, Loss: {avg_loss:.6f}")

        print("Training completed!")
    
    def predict(self, data):
        """
        Predict/impute missing values.
        """
        self.model.eval()

        with torch.no_grad():
            # Create mask for missing values
            missing_mask = torch.isnan(data)

            # Replace NaN with zeros for model input
            data_clean = torch.nan_to_num(data, nan=0.0)

            # Flatten the input data
            data_flat = data_clean.view(data_clean.size(0), -1)

            # Get reconstructed data
            reconstructed = self.model(data_flat)

            # Reshape back to original shape
            reconstructed = reconstructed.view(data.shape)

            # Create imputed data: use original values where available, predictions where missing
            imputed_data = data.clone()
            imputed_data[missing_mask] = reconstructed[missing_mask]

        return imputed_data
